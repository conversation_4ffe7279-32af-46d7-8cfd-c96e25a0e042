<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use App\Models\AcsCooperativeBranch;
use App\Models\AcsCooperative;
use App\Models\AcsBranchApprovalHistory;
use App\Notifications\BranchApprovalNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ListBranches extends Component
{
    use WithPagination;

    public $search = '';
    public $selectedStatus = '';
    public $selectedCooperative = '';
    public $cooperatives = [];
    public $perPage = 10;
    public $showFilters = false;

    // Modal properties
    public $showModal = false;
    public $selectedBranch = null;

    // Approval/Rejection modal properties
    public $showApprovalModal = false;
    public $showRejectionModal = false;
    public $approvalReason = '';
    public $rejectionReason = '';
    public $pendingActionBranch = null;
    public $debugMessage = 'Component loaded successfully';

    public function mount()
    {
        // Load cooperatives for the dropdown
        $this->cooperatives = AcsCooperative::orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        // Debug: Log mount method
        Log::info('ListBranches component mounted');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedCooperative()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedStatus = '';
        $this->selectedCooperative = '';
        $this->resetPage();
    }

    public function showBranchDetails($branchId)
    {
        $this->selectedBranch = AcsCooperativeBranch::with(['cooperative', 'creator', 'verifier', 'approvalHistory.actionBy'])
            ->findOrFail($branchId);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedBranch = null;
    }

        public function showApprovalModal($branchId)
    {
        $this->pendingActionBranch = AcsCooperativeBranch::findOrFail($branchId);
        $this->approvalReason = '';
        $this->showApprovalModal = true;

        // Debug: Log to see if method is being called
        Log::info('Approval modal opened for branch: ' . $branchId);
    }

    public function openApproval($branchId)
    {
        $this->pendingActionBranch = AcsCooperativeBranch::findOrFail($branchId);
        $this->approvalReason = '';
        $this->showApprovalModal = true;

        // Debug: Log to see if method is being called
        Log::info('Open approval modal for branch: ' . $branchId);
    }

        public function showRejectionModal($branchId)
    {
        $this->pendingActionBranch = AcsCooperativeBranch::findOrFail($branchId);
        $this->rejectionReason = '';
        $this->showRejectionModal = true;

        // Debug: Log to see if method is being called
        Log::info('Rejection modal opened for branch: ' . $branchId);
    }

    public function openRejection($branchId)
    {
        $this->pendingActionBranch = AcsCooperativeBranch::findOrFail($branchId);
        $this->rejectionReason = '';
        $this->showRejectionModal = true;

        // Debug: Log to see if method is being called
        Log::info('Open rejection modal for branch: ' . $branchId);
    }

    public function closeApprovalModal()
    {
        $this->showApprovalModal = false;
        $this->pendingActionBranch = null;
        $this->approvalReason = '';
    }

    public function closeRejectionModal()
    {
        $this->showRejectionModal = false;
        $this->pendingActionBranch = null;
        $this->rejectionReason = '';
    }

    public function testLivewire()
    {
        $this->debugMessage = 'Livewire is working! Tested at: ' . now();
        Log::info('Livewire test method called');
    }



    public function approveBranch($branchId)
    {
        // Validate approval reason if provided
        $this->validate([
            'approvalReason' => 'nullable|string|max:500',
        ], [
            'approvalReason.max' => 'Approval reason cannot exceed 500 characters.',
        ]);

        $branch = AcsCooperativeBranch::findOrFail($branchId);
        if ($branch->status !== 'pending') {
            $this->dispatch('error', 'Branch is not in pending status');
            return;
        }

        $previousStatus = $branch->status;

        DB::beginTransaction();
        try {
            // Update branch status
            $branch->update([
                'status' => 'active',
                'registration_token' => Str::uuid(),
                'verified_by' => Auth::user()->id,
                'verified_at' => now()
            ]);


            // Record approval history
            DB::table('acs_branch_approval_history')->insert([
                'acs_coorperative_branch_id' => $branch->id,
                'action' => 'approve',
                'reason' => $this->approvalReason,
                'action_by' => Auth::user()->id,
                'previous_status' => $previousStatus,
                'new_status' => 'active',
                'additional_data' => [
                    'email_sent' => false,
                    'email_sent_at' => null,
                ]
            ]);


            // Send email notification to branch creator
            if ($branch->creator && $branch->creator->email) {
                try {
                    $branch->creator->notify(new BranchApprovalNotification(
                        $branch,
                        'approve',
                        $this->approvalReason,
                        Auth::user()
                    ));

                    // Update history with email sent status
                    DB::table('acs_branch_approval_history')
                        ->where('acs_coorperative_branch_id', $branch->id)
                        ->where('action', 'approve')
                        ->update([
                            'additional_data' => [
                                'email_sent' => true,
                                'email_sent_at' => now()->toISOString(),
                            ]
                        ]);
                } catch (\Exception $e) {
                    // Log email error but don't fail the approval
                    Log::error('Failed to send branch approval email: ' . $e->getMessage());
                }
            }

            DB::commit();
            $this->closeApprovalModal();
            $this->dispatch('success', 'Branch has been approved successfully');
        } catch (\Exception $e) {
            dd()
            DB::rollBack();
            $this->dispatch('error', 'Failed to approve branch: ' . $e->getMessage());
        }
    }

    public function declineBranch($branchId)
    {
        // Validate rejection reason is required
        $this->validate([
            'rejectionReason' => 'required|string|min:10|max:500',
        ], [
            'rejectionReason.required' => 'Please provide a reason for rejection.',
            'rejectionReason.min' => 'Rejection reason must be at least 10 characters.',
            'rejectionReason.max' => 'Rejection reason cannot exceed 500 characters.',
        ]);

        $branch = AcsCooperativeBranch::findOrFail($branchId);
        if ($branch->status !== 'pending') {
            $this->dispatch('error', 'Branch is not in pending status');
            return;
        }

        $previousStatus = $branch->status;

        DB::beginTransaction();
        try {
            // Update branch status
            $branch->update([
                'status' => 'inactive',
                'verified_by' => Auth::user()->id,
                'verified_at' => now()
            ]);

            // Record rejection history
            DB::table('acs_branch_approval_history')->insert([
                'acs_coorperative_branch_id' => $branch->id,
                'action' => 'decline',
                'reason' => $this->rejectionReason,
                'action_by' => Auth::user()->id,
                'previous_status' => $previousStatus,
                'new_status' => 'inactive',
                'additional_data' => json_encode([
                    'email_sent' => false,
                    'email_sent_at' => null,
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Send email notification to branch creator
            if ($branch->creator && $branch->creator->email) {
                try {
                    $branch->creator->notify(new BranchApprovalNotification(
                        $branch,
                        'decline',
                        $this->rejectionReason,
                        Auth::user()
                    ));

                    // Update history with email sent status
                    DB::table('acs_branch_approval_history')
                        ->where('acs_coorperative_branch_id', $branch->id)
                        ->where('action', 'decline')
                        ->update([
                            'additional_data' => [
                                'email_sent' => true,
                                'email_sent_at' => now()->toISOString(),
                            ]
                        ]);
                } catch (\Exception $e) {
                    // Log email error but don't fail the rejection
                    Log::error('Failed to send branch rejection email: ' . $e->getMessage());
                }
            }


            DB::commit();
            $this->closeRejectionModal();
            $this->dispatch('success', 'Branch has been declined successfully');
        } catch (\Exception $e) {
            dd($e->getMessage());
            DB::rollBack();
            $this->dispatch('error', 'Failed to decline branch: ' . $e->getMessage());
        }
    }

    protected function getBranchesSummary()
    {
        return DB::table('acs_coorperative_branch as b')
            ->join('acs_coorperative as c', 'c.id', '=', 'b.acs_coorperative_id')
            ->select(
                'c.name as cooperative_name',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN b.status = "pending" THEN 1 ELSE 0 END) as pending'),
                DB::raw('SUM(CASE WHEN b.status = "active" THEN 1 ELSE 0 END) as active'),
                DB::raw('SUM(CASE WHEN b.status = "inactive" THEN 1 ELSE 0 END) as inactive')
            )
            ->whereNull('b.deleted_at')
            ->groupBy('c.id', 'c.name')
            ->orderBy('c.name')
            ->get();
    }

    public function render()
    {
        $branches = AcsCooperativeBranch::with(['cooperative', 'creator', 'verifier'])
            ->when($this->search, function($query) {
                $query->where(function($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('address', 'like', '%' . $this->search . '%')
                      ->orWhere('business_registration_no', 'like', '%' . $this->search . '%')
                      ->orWhereHas('cooperative', function($cq) {
                          $cq->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->selectedStatus, function($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->selectedCooperative, function($query) {
                $query->where('acs_coorperative_id', $this->selectedCooperative);
            })
            ->latest()
            ->paginate($this->perPage);

        return view('livewire.admin.list-branches', [
            'branches' => $branches,
            'branchesSummary' => $this->getBranchesSummary()
        ]);
    }
}
